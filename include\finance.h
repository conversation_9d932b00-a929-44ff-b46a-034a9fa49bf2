#ifndef FINANCE_H
#define FINANCE_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

// Constants
#define MAX_USERS 100
#define MAX_TRANSACTIONS 1000
#define MAX_CATEGORIES 20
#define MAX_NAME_LENGTH 50
#define MAX_CATEGORY_LENGTH 30
#define MAX_DESCRIPTION_LENGTH 100

// Global variables
extern int total_users;
extern int total_transactions;
extern int current_user_id;

// Structure definitions
typedef struct {
    int user_id;
    char name[MAX_NAME_LENGTH];
    char email[MAX_NAME_LENGTH];
    float total_income;
    float total_expenses;
    float current_balance;
} User;

typedef struct {
    int transaction_id;
    int user_id;
    char date[11]; // DD-MM-YYYY format
    char category[MAX_CATEGORY_LENGTH];
    char description[MAX_DESCRIPTION_LENGTH];
    float amount;
    char type; // 'I' for Income, 'E' for Expense
} Transaction;

typedef struct {
    int user_id;
    char category[MAX_CATEGORY_LENGTH];
    float budget_limit;
    float spent_amount;
    char month[8]; // MM-YYYY format
} Budget;

// Function declarations

// User management functions
int create_user(User users[], char* name, char* email);
int login_user(User users[], char* name);
void display_user_profile(User users[], int user_id);
void update_user_balance(User users[], int user_id, float amount, char type);

// Transaction functions
int add_transaction(Transaction transactions[], int user_id, char* date, 
                   char* category, char* description, float amount, char type);
void display_transactions(Transaction transactions[], int user_id);
void display_transaction_by_category(Transaction transactions[], int user_id, char* category);

// Budget functions
int set_budget(Budget budgets[], int user_id, char* category, float limit, char* month);
void check_budget_status(Budget budgets[], Transaction transactions[], int user_id);
void display_budgets(Budget budgets[], int user_id);

// Sorting functions (bubble sort and selection sort)
void bubble_sort_transactions_by_amount(Transaction transactions[], int count);
void selection_sort_transactions_by_date(Transaction transactions[], int count);

// Search functions
int search_transaction_by_id(Transaction transactions[], int transaction_id);
void search_transactions_by_category(Transaction transactions[], int user_id, char* category);

// File operations
void save_users_to_file(User users[]);
void load_users_from_file(User users[]);
void save_transactions_to_file(Transaction transactions[]);
void load_transactions_from_file(Transaction transactions[]);
void save_budgets_to_file(Budget budgets[]);
void load_budgets_from_file(Budget budgets[]);

// Report functions
void generate_monthly_report(User users[], Transaction transactions[], int user_id, char* month);
void generate_category_report(Transaction transactions[], int user_id);
void display_financial_summary(User users[], Transaction transactions[], int user_id);

// Utility functions
void get_current_date(char* date);
int validate_date(char* date);
float calculate_category_total(Transaction transactions[], int user_id, char* category, char type);
void clear_input_buffer(void);
void display_menu(void);

// Pointer manipulation functions
void process_transactions_with_pointers(Transaction* transactions, int count);
void sort_using_pointer_to_pointer(Transaction** trans_ptr, int count);

#endif // FINANCE_H
