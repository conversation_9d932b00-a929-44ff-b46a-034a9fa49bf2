# Personal Finance Management System (PFMS)

A comprehensive C programming project that demonstrates structured programming concepts while solving the real-world problem of personal financial management.

## 🎯 Problem Statement

Many individuals struggle with managing their personal finances, tracking expenses, budgeting, and understanding their spending patterns. This system provides a simple, efficient solution for monitoring financial health without relying on complex software or online services.

## 🚀 Features

### Core Functionality
- **User Account Management** - Create and manage multiple user profiles
- **Transaction Recording** - Add and track income/expense transactions
- **Budget Planning** - Set monthly budgets for different categories
- **Expense Tracking** - Monitor spending against budgets with alerts
- **Financial Reports** - Generate detailed summaries and analytics
- **Data Persistence** - Save/load data from files
- **Search & Filter** - Find specific transactions efficiently
- **Sorting Options** - Organize data by amount, date, or category

### Advanced Features
- **CSV Import/Export** - Exchange data with other applications
- **Backup System** - Create timestamped backups of all data
- **Financial Analysis** - Calculate ratios, trends, and recommendations
- **Budget Variance Analysis** - Compare planned vs actual spending
- **Pointer Operations Demo** - Advanced memory management examples

## 📚 C Programming Concepts Demonstrated

### ✅ All Required Topics Covered:

1. **Computer and C Fundamentals**
   - Program structure, compilation, execution
   - Header files and modular programming

2. **C Data Types**
   - `int`, `float`, `char`, arrays for various data storage
   - Custom data types using `typedef`

3. **Operators and Expressions**
   - Arithmetic: `+`, `-`, `*`, `/`, `%`
   - Comparison: `==`, `!=`, `<`, `>`, `<=`, `>=`
   - Logical: `&&`, `||`, `!`
   - Assignment: `=`, `+=`, `-=`

4. **Basic I/O Functions**
   - `printf()`, `scanf()`, `fgets()`, `getchar()`
   - File I/O: `fopen()`, `fread()`, `fwrite()`, `fclose()`

5. **If-else Statements**
   - Menu navigation and input validation
   - Financial health assessment
   - Budget status checking

6. **Loops**
   - **For loops**: Array iteration, data processing
   - **While loops**: Menu systems, file reading
   - **Do-while loops**: Input validation, confirmation prompts

7. **Functions**
   - Modular design with 50+ functions
   - Parameter passing and return values
   - Function prototypes in header files

8. **Variable Scopes**
   - **Global**: User count, transaction count
   - **Local**: Function parameters and temporary variables
   - **Static**: ID generation, menu access counting
   - **Register**: Optimization for frequently used variables

9. **Arrays**
   - User arrays, transaction arrays, budget arrays
   - Multi-dimensional arrays for category data
   - String arrays for names and descriptions

10. **Sorting Algorithms**
    - **Bubble Sort**: Sort transactions by amount
    - **Selection Sort**: Sort transactions by date

11. **Pointer Arithmetic**
    - Array traversal using pointers
    - Efficient memory access patterns
    - Pointer increment/decrement operations

12. **Arrays and Pointers**
    - Array-pointer equivalence
    - Passing arrays to functions
    - Dynamic array manipulation

13. **Pointer to Pointer**
    - Advanced sorting demonstrations
    - Complex data structure management
    - Memory indirection examples

14. **Structures**
    - `User` structure for account information
    - `Transaction` structure for financial records
    - `Budget` structure for spending limits

## 🏗️ Project Structure

```
Personal Finance Management System/
├── src/
│   ├── main.c              # Main program with menu system
│   ├── user.c              # User management functions
│   ├── transaction.c       # Transaction handling
│   ├── budget.c            # Budget management
│   ├── reports.c           # Report generation
│   ├── file_operations.c   # File I/O operations
│   └── utils.c             # Utility functions (sorting, searching)
├── include/
│   └── finance.h           # Header file with structures and declarations
├── obj/                    # Object files (created during compilation)
├── data/                   # Data files (created at runtime)
│   ├── users.dat           # User data file
│   ├── transactions.dat    # Transaction records
│   └── budgets.dat         # Budget information
├── Makefile               # Build system
└── README.md              # This file
```

## 🛠️ Compilation and Usage

### Prerequisites
- GCC compiler
- Make utility (optional but recommended)
- Terminal/Command prompt

### Building the Project

#### Using Make (Recommended):
```bash
# Build the project
make

# Build and run
make run

# Build debug version
make debug

# Build optimized release
make release

# Clean build files
make clean

# Show all available commands
make help
```

#### Manual Compilation:
```bash
# Create directories
mkdir -p obj data

# Compile all source files
gcc -Wall -Wextra -std=c99 -Iinclude -c src/*.c
mv *.o obj/

# Link and create executable
gcc obj/*.o -o finance_manager
```

### Running the Program
```bash
./finance_manager
```

## 📖 Usage Guide

### 1. Getting Started
1. Run the program
2. Create a new user account (Option 1)
3. Login with your username (Option 2)

### 2. Managing Transactions
- **Add Transaction** (Option 3): Record income or expenses
- **View Transactions** (Option 4): See all your financial records
- **Search Transactions** (Option 9): Find specific records

### 3. Budget Management
- **Set Budget** (Option 5): Create spending limits for categories
- **View Budget Status** (Option 6): Check spending against limits

### 4. Reports and Analysis
- **Generate Reports** (Option 7): Monthly, category, or summary reports
- **Sort Transactions** (Option 8): Organize by amount or date

### 5. Advanced Features
- **Pointer Operations** (Option 11): Demonstration of advanced concepts
- **Data Export**: CSV export functionality
- **Backup System**: Automatic data backup

## 💡 Key Programming Highlights

### Structured Programming Principles
- **Modularity**: Separate files for different functionalities
- **Abstraction**: Clear function interfaces and data structures
- **Encapsulation**: Data hiding through proper scope management
- **Reusability**: Generic utility functions used throughout

### Memory Management
- **Static Arrays**: Fixed-size data structures for predictable memory usage
- **Pointer Arithmetic**: Efficient array traversal and manipulation
- **File I/O**: Persistent data storage and retrieval

### Algorithm Implementation
- **Sorting**: Bubble sort and selection sort implementations
- **Searching**: Linear search with various criteria
- **Data Processing**: Statistical calculations and analysis

## 🔧 Customization

### Adding New Features
1. **New Transaction Types**: Modify the `Transaction` structure
2. **Additional Reports**: Add functions to `reports.c`
3. **Enhanced Validation**: Extend utility functions
4. **New Data Fields**: Update structures and file operations

### Configuration
- Modify constants in `finance.h` for limits and sizes
- Adjust date formats and validation rules
- Customize report layouts and calculations

## 🐛 Error Handling

The system includes comprehensive error handling for:
- File I/O operations
- Invalid user input
- Memory allocation issues
- Data validation errors
- Boundary condition checks

## 📊 Sample Output

```
==================================================
    PERSONAL FINANCE MANAGEMENT SYSTEM
==================================================
1.  Create New User
2.  Login
3.  Add Transaction
4.  View Transactions
5.  Set Budget
6.  View Budget Status
7.  Generate Reports
8.  Sort Transactions
9.  Search Transactions
10. View User Profile
11. Advanced Pointer Operations
0.  Exit
==================================================
```

## 🎓 Educational Value

This project serves as an excellent learning resource for:
- **C Programming Fundamentals**: All core concepts covered
- **Structured Programming**: Best practices and design patterns
- **Problem Solving**: Real-world application development
- **Software Engineering**: Modular design and documentation
- **Data Management**: File handling and data persistence

## 🤝 Contributing

Feel free to enhance this project by:
- Adding new features
- Improving algorithms
- Enhancing user interface
- Adding more comprehensive error handling
- Optimizing performance

## 📝 License

This project is created for educational purposes and demonstrates structured programming concepts in C.

---

**Note**: This project comprehensively covers all required C programming topics while solving a practical real-world problem, making it an ideal demonstration of structured programming principles.
