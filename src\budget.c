#include "../include/finance.h"

// Global variable for budget count
static int total_budgets = 0;

/**
 * Set budget for a category
 * Uses structures, arrays, and string operations
 */
int set_budget(Budget budgets[], int user_id, char* category, float limit, char* month) {
    // Validate budget limit
    if(limit <= 0) {
        printf("Budget limit must be positive!\n");
        return -1;
    }
    
    // Check if budget already exists for this category and month
    for(int i = 0; i < total_budgets; i++) {
        if(budgets[i].user_id == user_id && 
           strcmp(budgets[i].category, category) == 0 && 
           strcmp(budgets[i].month, month) == 0) {
            
            // Update existing budget
            printf("Budget already exists. Updating from $%.2f to $%.2f\n", 
                   budgets[i].budget_limit, limit);
            budgets[i].budget_limit = limit;
            return budgets[i].user_id;
        }
    }
    
    // Create new budget
    budgets[total_budgets].user_id = user_id;
    strcpy(budgets[total_budgets].category, category);
    budgets[total_budgets].budget_limit = limit;
    budgets[total_budgets].spent_amount = 0.0;
    strcpy(budgets[total_budgets].month, month);
    
    total_budgets++;
    return user_id;
}

/**
 * Check budget status against actual spending
 * Uses loops, conditional statements, and calculations
 */
void check_budget_status(Budget budgets[], Transaction transactions[], int user_id) {
    printf("\n=== Budget Status Report ===\n");
    
    int found_budgets = 0;
    
    // Iterate through all budgets for the user
    for(int i = 0; i < total_budgets; i++) {
        if(budgets[i].user_id == user_id) {
            found_budgets = 1;
            
            // Calculate actual spending for this category and month
            float actual_spending = 0.0;
            
            // Use nested loop to find matching transactions
            for(int j = 0; j < total_transactions; j++) {
                if(transactions[j].user_id == user_id && 
                   transactions[j].type == 'E' && 
                   strcmp(transactions[j].category, budgets[i].category) == 0) {
                    
                    // Extract month-year from transaction date
                    char trans_month[8];
                    strncpy(trans_month, transactions[j].date + 3, 7);
                    trans_month[7] = '\0';
                    
                    if(strcmp(trans_month, budgets[i].month) == 0) {
                        actual_spending += transactions[j].amount;
                    }
                }
            }
            
            // Update spent amount in budget
            budgets[i].spent_amount = actual_spending;
            
            // Display budget status
            printf("\nCategory: %s (Month: %s)\n", budgets[i].category, budgets[i].month);
            printf("Budget Limit: $%.2f\n", budgets[i].budget_limit);
            printf("Amount Spent: $%.2f\n", actual_spending);
            printf("Remaining: $%.2f\n", budgets[i].budget_limit - actual_spending);
            
            // Calculate percentage using arithmetic operators
            float percentage = (actual_spending / budgets[i].budget_limit) * 100.0;
            printf("Percentage Used: %.1f%%\n", percentage);
            
            // Budget status using if-else statements
            if(actual_spending > budgets[i].budget_limit) {
                printf("Status: OVER BUDGET! (Exceeded by $%.2f)\n", 
                       actual_spending - budgets[i].budget_limit);
            } else if(percentage >= 90.0) {
                printf("Status: WARNING - Nearly at limit!\n");
            } else if(percentage >= 75.0) {
                printf("Status: CAUTION - 75%% of budget used\n");
            } else if(percentage >= 50.0) {
                printf("Status: GOOD - Halfway through budget\n");
            } else {
                printf("Status: EXCELLENT - Well within budget\n");
            }
            
            printf("----------------------------------------\n");
        }
    }
    
    if(!found_budgets) {
        printf("No budgets set yet. Create some budgets to track your spending!\n");
    }
}

/**
 * Display all budgets for a user
 * Uses formatted output and loops
 */
void display_budgets(Budget budgets[], int user_id) {
    printf("\n=== Your Budgets ===\n");
    printf("%-15s %-10s %-10s %-10s %-10s %-10s\n", 
           "Category", "Month", "Limit", "Spent", "Remaining", "Status");
    printf("------------------------------------------------------------------------\n");
    
    int found = 0;
    
    for(int i = 0; i < total_budgets; i++) {
        if(budgets[i].user_id == user_id) {
            found = 1;
            
            float remaining = budgets[i].budget_limit - budgets[i].spent_amount;
            float percentage = (budgets[i].spent_amount / budgets[i].budget_limit) * 100.0;
            
            char status[15];
            if(budgets[i].spent_amount > budgets[i].budget_limit) {
                strcpy(status, "OVER");
            } else if(percentage >= 90.0) {
                strcpy(status, "WARNING");
            } else if(percentage >= 75.0) {
                strcpy(status, "CAUTION");
            } else {
                strcpy(status, "GOOD");
            }
            
            printf("%-15s %-10s $%-9.2f $%-9.2f $%-9.2f %-10s\n",
                   budgets[i].category,
                   budgets[i].month,
                   budgets[i].budget_limit,
                   budgets[i].spent_amount,
                   remaining,
                   status);
        }
    }
    
    if(!found) {
        printf("No budgets found.\n");
    }
}

/**
 * Calculate total budget for a month
 * Uses loops and arithmetic operations
 */
float calculate_total_monthly_budget(Budget budgets[], int user_id, char* month) {
    register float total = 0.0; // Register variable for optimization
    
    for(register int i = 0; i < total_budgets; i++) {
        if(budgets[i].user_id == user_id && strcmp(budgets[i].month, month) == 0) {
            total += budgets[i].budget_limit;
        }
    }
    
    return total;
}

/**
 * Find budget by category and month
 * Uses linear search and string comparison
 */
Budget* find_budget(Budget budgets[], int user_id, char* category, char* month) {
    Budget* budget_ptr = budgets; // Pointer to budgets array
    
    // Use pointer arithmetic for search
    for(int i = 0; i < total_budgets; i++, budget_ptr++) {
        if(budget_ptr->user_id == user_id && 
           strcmp(budget_ptr->category, category) == 0 && 
           strcmp(budget_ptr->month, month) == 0) {
            return budget_ptr;
        }
    }
    
    return NULL; // Budget not found
}

/**
 * Delete a budget
 * Uses array manipulation and pointer arithmetic
 */
int delete_budget(Budget budgets[], int user_id, char* category, char* month) {
    int found_index = -1;
    
    // Find the budget to delete
    for(int i = 0; i < total_budgets; i++) {
        if(budgets[i].user_id == user_id && 
           strcmp(budgets[i].category, category) == 0 && 
           strcmp(budgets[i].month, month) == 0) {
            found_index = i;
            break;
        }
    }
    
    if(found_index == -1) {
        return 0; // Budget not found
    }
    
    // Shift all budgets after the deleted one using pointer arithmetic
    Budget* budget_ptr = budgets + found_index;
    for(int i = found_index; i < total_budgets - 1; i++) {
        *budget_ptr = *(budget_ptr + 1);
        budget_ptr++;
    }
    
    total_budgets--;
    return 1; // Success
}

/**
 * Get budget recommendations based on spending patterns
 * Uses statistical analysis and conditional logic
 */
void get_budget_recommendations(Budget budgets[], Transaction transactions[], int user_id) {
    printf("\n=== Budget Recommendations ===\n");
    
    // Analyze spending patterns for the last 3 months
    char categories[MAX_CATEGORIES][MAX_CATEGORY_LENGTH];
    float category_totals[MAX_CATEGORIES];
    int category_count = 0;
    
    // Collect unique categories and their totals
    for(int i = 0; i < total_transactions; i++) {
        if(transactions[i].user_id == user_id && transactions[i].type == 'E') {
            
            // Check if category already exists
            int found = 0;
            for(int j = 0; j < category_count; j++) {
                if(strcmp(categories[j], transactions[i].category) == 0) {
                    category_totals[j] += transactions[i].amount;
                    found = 1;
                    break;
                }
            }
            
            // Add new category
            if(!found && category_count < MAX_CATEGORIES) {
                strcpy(categories[category_count], transactions[i].category);
                category_totals[category_count] = transactions[i].amount;
                category_count++;
            }
        }
    }
    
    // Generate recommendations using conditional statements
    if(category_count == 0) {
        printf("No expense data available for recommendations.\n");
        return;
    }
    
    printf("Based on your spending patterns:\n\n");
    
    for(int i = 0; i < category_count; i++) {
        float avg_monthly = category_totals[i] / 3.0; // Assume 3 months of data
        float recommended_budget = avg_monthly * 1.1; // 10% buffer
        
        printf("Category: %s\n", categories[i]);
        printf("  Average monthly spending: $%.2f\n", avg_monthly);
        printf("  Recommended budget: $%.2f\n", recommended_budget);
        
        // Check if budget already exists
        Budget* existing = find_budget(budgets, user_id, categories[i], "current");
        if(existing != NULL) {
            if(existing->budget_limit < recommended_budget) {
                printf("  Suggestion: Consider increasing your budget\n");
            } else if(existing->budget_limit > recommended_budget * 1.5) {
                printf("  Suggestion: You might be able to reduce this budget\n");
            } else {
                printf("  Suggestion: Your current budget looks appropriate\n");
            }
        } else {
            printf("  Suggestion: Set a budget of $%.2f for this category\n", recommended_budget);
        }
        printf("\n");
    }
}

/**
 * Calculate budget variance
 * Uses statistical calculations and loops
 */
void calculate_budget_variance(Budget budgets[], int user_id) {
    printf("\n=== Budget Variance Analysis ===\n");
    
    float total_budgeted = 0.0;
    float total_spent = 0.0;
    int budget_count = 0;
    
    // Calculate totals using while loop
    int i = 0;
    while(i < total_budgets) {
        if(budgets[i].user_id == user_id) {
            total_budgeted += budgets[i].budget_limit;
            total_spent += budgets[i].spent_amount;
            budget_count++;
        }
        i++;
    }
    
    if(budget_count == 0) {
        printf("No budgets found for variance analysis.\n");
        return;
    }
    
    float variance = total_spent - total_budgeted;
    float variance_percentage = (variance / total_budgeted) * 100.0;
    
    printf("Total Budgeted: $%.2f\n", total_budgeted);
    printf("Total Spent: $%.2f\n", total_spent);
    printf("Variance: $%.2f (%.1f%%)\n", variance, variance_percentage);
    
    // Analysis using conditional operators
    if(variance > 0) {
        printf("Analysis: You are over budget by $%.2f\n", variance);
        printf("Recommendation: Review your spending and adjust budgets\n");
    } else if(variance < -100) {
        printf("Analysis: You are significantly under budget\n");
        printf("Recommendation: Consider reallocating unused budget\n");
    } else {
        printf("Analysis: Your spending is well-aligned with your budget\n");
        printf("Recommendation: Continue with current budget strategy\n");
    }
}
