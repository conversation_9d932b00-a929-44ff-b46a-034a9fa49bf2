#!/bin/bash

# Personal Finance Management System - Demo Script
# This script demonstrates the functionality of the PFMS

echo "=== Personal Finance Management System Demo ==="
echo "This demo will show the key features of the system."
echo ""

# Compile the project
echo "1. Compiling the project..."
mkdir -p obj data
gcc -Wall -Wextra -std=c99 -Iinclude -c src/*.c
mv *.o obj/ 2>/dev/null
gcc obj/*.o -o finance_manager

if [ $? -eq 0 ]; then
    echo "✓ Compilation successful!"
else
    echo "✗ Compilation failed!"
    exit 1
fi

echo ""
echo "2. Project structure:"
echo "   src/           - Source code files"
echo "   include/       - Header files"
echo "   obj/           - Compiled object files"
echo "   data/          - Data storage directory"
echo ""

echo "3. Key features implemented:"
echo "   ✓ User account management"
echo "   ✓ Transaction recording (income/expenses)"
echo "   ✓ Budget planning and tracking"
echo "   ✓ Financial reports and analytics"
echo "   ✓ Data persistence (file I/O)"
echo "   ✓ Sorting algorithms (bubble sort, selection sort)"
echo "   ✓ Search functionality"
echo "   ✓ Pointer arithmetic demonstrations"
echo ""

echo "4. C Programming concepts demonstrated:"
echo "   ✓ Computer and C Fundamentals"
echo "   ✓ C Data types (int, float, char, arrays)"
echo "   ✓ Operators and Expressions"
echo "   ✓ Basic I/O functions"
echo "   ✓ If-else statements"
echo "   ✓ Loops (for, while, do-while)"
echo "   ✓ Functions with different scopes"
echo "   ✓ Global, local, static, and register variables"
echo "   ✓ Arrays and array manipulation"
echo "   ✓ Bubble sort and selection sort"
echo "   ✓ Pointer arithmetic"
echo "   ✓ Arrays and pointers"
echo "   ✓ Pointer to pointer concepts"
echo "   ✓ Structures (User, Transaction, Budget)"
echo ""

echo "5. Files created:"
ls -la src/ include/ obj/ 2>/dev/null | head -20

echo ""
echo "6. To run the program:"
echo "   ./finance_manager"
echo ""

echo "7. Sample usage workflow:"
echo "   a) Create a new user account"
echo "   b) Login with your username"
echo "   c) Add some income and expense transactions"
echo "   d) Set budgets for different categories"
echo "   e) View reports and analyze your finances"
echo "   f) Try sorting and searching features"
echo "   g) Explore advanced pointer operations"
echo ""

echo "Demo completed! The Personal Finance Management System is ready to use."
echo "This project demonstrates all required C programming concepts while"
echo "solving the real-world problem of personal financial management."
