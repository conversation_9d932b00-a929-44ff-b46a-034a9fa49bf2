# Personal Finance Management System Makefile
# Demonstrates compilation of multiple C source files

# Compiler and flags
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g
INCLUDES = -Iinclude

# Directories
SRCDIR = src
INCDIR = include
OBJDIR = obj
DATADIR = data

# Source files
SOURCES = $(wildcard $(SRCDIR)/*.c)
OBJECTS = $(SOURCES:$(SRCDIR)/%.c=$(OBJDIR)/%.o)

# Target executable
TARGET = finance_manager

# Default target
all: $(TARGET)

# Create target executable
$(TARGET): $(OBJECTS) | $(DATADIR)
	$(CC) $(OBJECTS) -o $@
	@echo "Build successful! Run with: ./$(TARGET)"

# Compile source files to object files
$(OBJDIR)/%.o: $(SRCDIR)/%.c | $(OBJDIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Create directories if they don't exist
$(OBJDIR):
	mkdir -p $(OBJDIR)

$(DATADIR):
	mkdir -p $(DATADIR)

# Clean build files
clean:
	rm -rf $(OBJDIR) $(TARGET)
	@echo "Clean completed"

# Clean all including data files
clean-all: clean
	rm -rf $(DATADIR) *.dat backup_*.dat *.csv
	@echo "All files cleaned"

# Install (copy to system directory - optional)
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/
	@echo "Installed to /usr/local/bin/"

# Uninstall
uninstall:
	rm -f /usr/local/bin/$(TARGET)
	@echo "Uninstalled from /usr/local/bin/"

# Run the program
run: $(TARGET)
	./$(TARGET)

# Debug build
debug: CFLAGS += -DDEBUG -O0
debug: $(TARGET)

# Release build
release: CFLAGS += -O2 -DNDEBUG
release: clean $(TARGET)

# Check for memory leaks (requires valgrind)
memcheck: $(TARGET)
	valgrind --leak-check=full --show-leak-kinds=all ./$(TARGET)

# Create a backup of source code
backup:
	tar -czf finance_manager_backup_$(shell date +%Y%m%d_%H%M%S).tar.gz src/ include/ Makefile README.md

# Show help
help:
	@echo "Personal Finance Management System - Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  all        - Build the project (default)"
	@echo "  clean      - Remove build files"
	@echo "  clean-all  - Remove all files including data"
	@echo "  run        - Build and run the program"
	@echo "  debug      - Build with debug flags"
	@echo "  release    - Build optimized release version"
	@echo "  install    - Install to system directory"
	@echo "  uninstall  - Remove from system directory"
	@echo "  memcheck   - Run with valgrind memory checker"
	@echo "  backup     - Create source code backup"
	@echo "  help       - Show this help message"

# Phony targets
.PHONY: all clean clean-all install uninstall run debug release memcheck backup help

# Dependencies (automatically generated)
-include $(OBJECTS:.o=.d)

# Generate dependency files
$(OBJDIR)/%.d: $(SRCDIR)/%.c | $(OBJDIR)
	@$(CC) $(CFLAGS) $(INCLUDES) -MM -MT $(@:.d=.o) $< > $@
