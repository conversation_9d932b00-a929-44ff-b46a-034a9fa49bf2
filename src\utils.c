#include "../include/finance.h"

// External global variables
extern int total_transactions;

/**
 * Bubble Sort implementation for sorting transactions by amount
 * Demonstrates bubble sort algorithm with arrays and loops
 */
void bubble_sort_transactions_by_amount(Transaction transactions[], int count) {
    if(count <= 1) return;

    // Bubble sort using nested for loops
    for(int i = 0; i < count - 1; i++) {
        for(int j = 0; j < count - i - 1; j++) {
            // Compare adjacent elements
            if(transactions[j].amount > transactions[j + 1].amount) {
                // Swap transactions
                Transaction temp = transactions[j];
                transactions[j] = transactions[j + 1];
                transactions[j + 1] = temp;
            }
        }
    }
}

/**
 * Selection Sort implementation for sorting transactions by date
 * Demonstrates selection sort algorithm with string comparison
 */
void selection_sort_transactions_by_date(Transaction transactions[], int count) {
    if(count <= 1) return;

    // Selection sort using nested loops
    for(int i = 0; i < count - 1; i++) {
        int min_index = i;

        // Find minimum element in remaining array
        for(int j = i + 1; j < count; j++) {
            // Compare dates (DD-MM-YYYY format)
            if(strcmp(transactions[j].date, transactions[min_index].date) < 0) {
                min_index = j;
            }
        }

        // Swap if minimum is not at current position
        if(min_index != i) {
            Transaction temp = transactions[i];
            transactions[i] = transactions[min_index];
            transactions[min_index] = temp;
        }
    }
}

/**
 * Get current date in DD-MM-YYYY format
 * Uses time functions and string formatting
 */
void get_current_date(char* date) {
    time_t t = time(NULL);
    struct tm* tm_info = localtime(&t);

    sprintf(date, "%02d-%02d-%04d",
            tm_info->tm_mday,
            tm_info->tm_mon + 1,
            tm_info->tm_year + 1900);
}

/**
 * Validate date format (DD-MM-YYYY)
 * Uses string manipulation and conditional logic
 */
int validate_date(char* date) {
    if(strlen(date) != 10) return 0;

    // Check format: DD-MM-YYYY
    if(date[2] != '-' || date[5] != '-') return 0;

    // Extract day, month, year
    int day, month, year;
    if(sscanf(date, "%d-%d-%d", &day, &month, &year) != 3) return 0;

    // Basic validation using if-else statements
    if(day < 1 || day > 31) return 0;
    if(month < 1 || month > 12) return 0;
    if(year < 1900 || year > 2100) return 0;

    // Additional validation for months with fewer days
    if(month == 2) { // February
        if(day > 29) return 0;
        if(day == 29 && year % 4 != 0) return 0; // Simple leap year check
    } else if(month == 4 || month == 6 || month == 9 || month == 11) {
        if(day > 30) return 0;
    }

    return 1; // Valid date
}

/**
 * Clear input buffer
 * Uses while loop and getchar()
 */
void clear_input_buffer(void) {
    int c;
    while((c = getchar()) != '\n' && c != EOF);
}

/**
 * Display main menu
 * Uses basic I/O functions
 */
void display_menu(void) {
    printf("\n==================================================\n");
    printf("    PERSONAL FINANCE MANAGEMENT SYSTEM\n");
    printf("==================================================\n");
    printf("1.  Create New User\n");
    printf("2.  Login\n");
    printf("3.  Add Transaction\n");
    printf("4.  View Transactions\n");
    printf("5.  Set Budget\n");
    printf("6.  View Budget Status\n");
    printf("7.  Generate Reports\n");
    printf("8.  Sort Transactions\n");
    printf("9.  Search Transactions\n");
    printf("10. View User Profile\n");
    printf("11. Advanced Pointer Operations\n");
    printf("0.  Exit\n");
    printf("==================================================\n");
}

/**
 * Process transactions using pointer arithmetic
 * Demonstrates pointer manipulation and arithmetic
 */
void process_transactions_with_pointers(Transaction* transactions, int count) {
    if(count == 0) {
        printf("No transactions to process.\n");
        return;
    }

    Transaction* ptr = transactions; // Pointer to first transaction
    float total_amount = 0.0;
    int income_count = 0, expense_count = 0;

    printf("Processing transactions using pointer arithmetic:\n");
    printf("%-5s %-10s %-5s\n", "ID", "Amount", "Type");
    printf("----------------------\n");

    // Use pointer arithmetic to traverse array
    for(int i = 0; i < count; i++) {
        printf("%-5d $%-9.2f %-5c\n",
               ptr->transaction_id,
               ptr->amount,
               ptr->type);

        // Calculate totals using pointer dereferencing
        if(ptr->type == 'I') {
            total_amount += ptr->amount;
            income_count++;
        } else if(ptr->type == 'E') {
            total_amount -= ptr->amount;
            expense_count++;
        }

        ptr++; // Move pointer to next transaction
    }

    printf("----------------------\n");
    printf("Net Amount: $%.2f\n", total_amount);
    printf("Income transactions: %d\n", income_count);
    printf("Expense transactions: %d\n", expense_count);
}

/**
 * Sort using pointer to pointer
 * Demonstrates pointer to pointer concept
 */
void sort_using_pointer_to_pointer(Transaction** trans_ptr, int count) {
    if(count <= 1) return;

    Transaction* transactions = *trans_ptr; // Dereference pointer to pointer

    printf("Sorting transactions by amount using pointer to pointer...\n");

    // Simple bubble sort using pointer to pointer
    for(int i = 0; i < count - 1; i++) {
        for(int j = 0; j < count - i - 1; j++) {
            Transaction* current = transactions + j;     // Pointer arithmetic
            Transaction* next = transactions + j + 1;    // Pointer arithmetic

            if(current->amount > next->amount) {
                // Swap using temporary variable
                Transaction temp = *current;
                *current = *next;
                *next = temp;
            }
        }
    }

    printf("Transactions sorted successfully!\n");
    printf("First transaction amount: $%.2f\n", transactions->amount);
    printf("Last transaction amount: $%.2f\n", (transactions + count - 1)->amount);
}

/**
 * Convert string to uppercase
 * Uses character manipulation and loops
 */
void string_to_upper(char* str) {
    char* ptr = str;

    while(*ptr) {
        if(*ptr >= 'a' && *ptr <= 'z') {
            *ptr = *ptr - 'a' + 'A'; // Convert to uppercase
        }
        ptr++;
    }
}

/**
 * Calculate compound interest
 * Uses mathematical operators and expressions
 */
float calculate_compound_interest(float principal, float rate, int time, int n) {
    float amount = principal;
    float rate_per_period = rate / (100.0 * n);
    int total_periods = n * time;

    // Calculate compound interest using for loop
    for(int i = 0; i < total_periods; i++) {
        amount = amount * (1.0 + rate_per_period);
    }

    return amount - principal; // Return interest earned
}

/**
 * Generate random transaction ID
 * Uses register variables and arithmetic operations
 */
int generate_random_id(void) {
    static int seed = 1; // Static variable to maintain state
    register int temp; // Register variable for fast access

    seed = (seed * 1103515245 + 12345) & 0x7fffffff; // Simple LCG
    temp = seed % 10000; // Limit to 4 digits

    return temp;
}

/**
 * Format currency display
 * Uses string formatting and conditional operators
 */
void format_currency(float amount, char* formatted) {
    if(amount >= 0) {
        sprintf(formatted, "$%.2f", amount);
    } else {
        sprintf(formatted, "-$%.2f", -amount);
    }
}

/**
 * Calculate percentage
 * Uses arithmetic operators and expressions
 */
float calculate_percentage(float part, float whole) {
    return (whole != 0.0) ? (part / whole) * 100.0 : 0.0;
}

/**
 * Find maximum value in array using pointers
 * Demonstrates pointer usage with arrays
 */
float find_max_amount(Transaction* transactions, int count) {
    if(count == 0) return 0.0;

    float max = transactions->amount; // First element
    Transaction* ptr = transactions + 1; // Start from second element

    // Use pointer arithmetic to find maximum
    for(int i = 1; i < count; i++, ptr++) {
        if(ptr->amount > max) {
            max = ptr->amount;
        }
    }

    return max;
}

/**
 * Find minimum value in array using pointers
 * Demonstrates pointer usage with arrays
 */
float find_min_amount(Transaction* transactions, int count) {
    if(count == 0) return 0.0;

    float min = transactions->amount; // First element
    Transaction* ptr = transactions + 1; // Start from second element

    // Use pointer arithmetic to find minimum
    for(int i = 1; i < count; i++, ptr++) {
        if(ptr->amount < min) {
            min = ptr->amount;
        }
    }

    return min;
}

/**
 * Count transactions by type using do-while loop
 * Demonstrates do-while loop usage
 */
void count_transactions_by_type(Transaction transactions[], int user_id,
                               int* income_count, int* expense_count) {
    *income_count = 0;
    *expense_count = 0;

    if(total_transactions == 0) return;

    int i = 0;
    do {
        if(transactions[i].user_id == user_id) {
            if(transactions[i].type == 'I') {
                (*income_count)++;
            } else if(transactions[i].type == 'E') {
                (*expense_count)++;
            }
        }
        i++;
    } while(i < total_transactions);
}
