#include "../include/finance.h"

// External global variables
extern int total_transactions;

// Static variable for transaction ID generation
static int next_transaction_id = 1;

/**
 * Add a new transaction
 * Uses structures, arrays, and string operations
 */
int add_transaction(Transaction transactions[], int user_id, char* date, 
                   char* category, char* description, float amount, char type) {
    
    // Check if maximum transactions reached
    if(total_transactions >= MAX_TRANSACTIONS) {
        return -1;
    }
    
    // Validate amount using conditional operators
    if(amount <= 0) {
        printf("Amount must be positive!\n");
        return -1;
    }
    
    // Create new transaction
    transactions[total_transactions].transaction_id = next_transaction_id++;
    transactions[total_transactions].user_id = user_id;
    strcpy(transactions[total_transactions].date, date);
    strcpy(transactions[total_transactions].category, category);
    strcpy(transactions[total_transactions].description, description);
    transactions[total_transactions].amount = amount;
    transactions[total_transactions].type = type;
    
    total_transactions++;
    return transactions[total_transactions - 1].transaction_id;
}

/**
 * Display all transactions for a user
 * Uses loops and formatted output
 */
void display_transactions(Transaction transactions[], int user_id) {
    printf("\n=== Your Transactions ===\n");
    printf("%-5s %-12s %-15s %-20s %-10s %-5s\n", 
           "ID", "Date", "Category", "Description", "Amount", "Type");
    printf("----------------------------------------------------------------\n");
    
    int found = 0;
    
    // Use for loop to iterate through transactions
    for(int i = 0; i < total_transactions; i++) {
        if(transactions[i].user_id == user_id) {
            printf("%-5d %-12s %-15s %-20s $%-9.2f %-5c\n",
                   transactions[i].transaction_id,
                   transactions[i].date,
                   transactions[i].category,
                   transactions[i].description,
                   transactions[i].amount,
                   transactions[i].type);
            found = 1;
        }
    }
    
    if(!found) {
        printf("No transactions found.\n");
    }
}

/**
 * Display transactions by category
 * Uses string comparison and conditional logic
 */
void display_transaction_by_category(Transaction transactions[], int user_id, char* category) {
    printf("\n=== Transactions in Category: %s ===\n", category);
    printf("%-5s %-12s %-20s %-10s %-5s\n", 
           "ID", "Date", "Description", "Amount", "Type");
    printf("------------------------------------------------\n");
    
    int found = 0;
    float total_amount = 0.0;
    
    for(int i = 0; i < total_transactions; i++) {
        if(transactions[i].user_id == user_id && 
           strcmp(transactions[i].category, category) == 0) {
            printf("%-5d %-12s %-20s $%-9.2f %-5c\n",
                   transactions[i].transaction_id,
                   transactions[i].date,
                   transactions[i].description,
                   transactions[i].amount,
                   transactions[i].type);
            
            // Calculate total using arithmetic operators
            if(transactions[i].type == 'I') {
                total_amount += transactions[i].amount;
            } else {
                total_amount -= transactions[i].amount;
            }
            found = 1;
        }
    }
    
    if(found) {
        printf("------------------------------------------------\n");
        printf("Net Amount in %s: $%.2f\n", category, total_amount);
    } else {
        printf("No transactions found in this category.\n");
    }
}

/**
 * Search transaction by ID
 * Uses linear search algorithm
 */
int search_transaction_by_id(Transaction transactions[], int transaction_id) {
    // Linear search using while loop
    int i = 0;
    while(i < total_transactions) {
        if(transactions[i].transaction_id == transaction_id) {
            return i; // Return index of found transaction
        }
        i++;
    }
    return -1; // Transaction not found
}

/**
 * Search transactions by category
 * Uses string matching and arrays
 */
void search_transactions_by_category(Transaction transactions[], int user_id, char* category) {
    printf("\n=== Search Results for Category: %s ===\n", category);
    
    int found_count = 0;
    Transaction found_transactions[MAX_TRANSACTIONS];
    
    // Collect matching transactions
    for(int i = 0; i < total_transactions; i++) {
        if(transactions[i].user_id == user_id && 
           strstr(transactions[i].category, category) != NULL) {
            found_transactions[found_count++] = transactions[i];
        }
    }
    
    if(found_count > 0) {
        printf("Found %d transaction(s):\n", found_count);
        printf("%-5s %-12s %-15s %-20s %-10s %-5s\n", 
               "ID", "Date", "Category", "Description", "Amount", "Type");
        printf("----------------------------------------------------------------\n");
        
        for(int i = 0; i < found_count; i++) {
            printf("%-5d %-12s %-15s %-20s $%-9.2f %-5c\n",
                   found_transactions[i].transaction_id,
                   found_transactions[i].date,
                   found_transactions[i].category,
                   found_transactions[i].description,
                   found_transactions[i].amount,
                   found_transactions[i].type);
        }
    } else {
        printf("No transactions found matching category: %s\n", category);
    }
}

/**
 * Calculate total amount for a specific category and type
 * Uses loops and conditional operators
 */
float calculate_category_total(Transaction transactions[], int user_id, char* category, char type) {
    register float total = 0.0; // Register variable for optimization
    
    for(register int i = 0; i < total_transactions; i++) {
        if(transactions[i].user_id == user_id && 
           strcmp(transactions[i].category, category) == 0 && 
           transactions[i].type == type) {
            total += transactions[i].amount;
        }
    }
    
    return total;
}

/**
 * Get transactions for a specific month
 * Uses string manipulation and date comparison
 */
int get_monthly_transactions(Transaction transactions[], int user_id, char* month, 
                           Transaction monthly_trans[]) {
    int count = 0;
    
    for(int i = 0; i < total_transactions; i++) {
        if(transactions[i].user_id == user_id) {
            // Extract month-year from date (DD-MM-YYYY -> MM-YYYY)
            char trans_month[8];
            strncpy(trans_month, transactions[i].date + 3, 7); // Skip DD- part
            trans_month[7] = '\0';
            
            if(strcmp(trans_month, month) == 0) {
                monthly_trans[count++] = transactions[i];
            }
        }
    }
    
    return count;
}

/**
 * Delete a transaction
 * Uses array manipulation and pointer arithmetic
 */
int delete_transaction(Transaction transactions[], int transaction_id, int user_id) {
    int found_index = -1;
    
    // Find the transaction
    for(int i = 0; i < total_transactions; i++) {
        if(transactions[i].transaction_id == transaction_id && 
           transactions[i].user_id == user_id) {
            found_index = i;
            break;
        }
    }
    
    if(found_index == -1) {
        return 0; // Transaction not found
    }
    
    // Shift all transactions after the deleted one
    Transaction* trans_ptr = transactions + found_index;
    for(int i = found_index; i < total_transactions - 1; i++) {
        *trans_ptr = *(trans_ptr + 1); // Pointer arithmetic
        trans_ptr++;
    }
    
    total_transactions--;
    return 1; // Success
}

/**
 * Update transaction amount
 * Uses pointer manipulation and validation
 */
int update_transaction_amount(Transaction transactions[], int transaction_id, 
                            int user_id, float new_amount) {
    if(new_amount <= 0) {
        printf("Amount must be positive!\n");
        return 0;
    }
    
    Transaction* trans_ptr = transactions;
    
    // Use pointer arithmetic to find transaction
    for(int i = 0; i < total_transactions; i++, trans_ptr++) {
        if(trans_ptr->transaction_id == transaction_id && 
           trans_ptr->user_id == user_id) {
            
            float old_amount = trans_ptr->amount;
            trans_ptr->amount = new_amount;
            
            printf("Transaction amount updated from $%.2f to $%.2f\n", 
                   old_amount, new_amount);
            return 1; // Success
        }
    }
    
    return 0; // Transaction not found
}

/**
 * Get transaction statistics
 * Uses multiple loops and calculations
 */
void get_transaction_statistics(Transaction transactions[], int user_id) {
    if(total_transactions == 0) {
        printf("No transactions to analyze.\n");
        return;
    }
    
    float total_income = 0.0, total_expenses = 0.0;
    int income_count = 0, expense_count = 0;
    float max_income = 0.0, max_expense = 0.0;
    float min_income = 999999.0, min_expense = 999999.0;
    
    // Calculate statistics using do-while loop
    int i = 0;
    do {
        if(transactions[i].user_id == user_id) {
            if(transactions[i].type == 'I') {
                total_income += transactions[i].amount;
                income_count++;
                if(transactions[i].amount > max_income) max_income = transactions[i].amount;
                if(transactions[i].amount < min_income) min_income = transactions[i].amount;
            } else if(transactions[i].type == 'E') {
                total_expenses += transactions[i].amount;
                expense_count++;
                if(transactions[i].amount > max_expense) max_expense = transactions[i].amount;
                if(transactions[i].amount < min_expense) min_expense = transactions[i].amount;
            }
        }
        i++;
    } while(i < total_transactions);
    
    printf("\n=== Transaction Statistics ===\n");
    printf("Total Income: $%.2f (%d transactions)\n", total_income, income_count);
    printf("Total Expenses: $%.2f (%d transactions)\n", total_expenses, expense_count);
    printf("Net Amount: $%.2f\n", total_income - total_expenses);
    
    if(income_count > 0) {
        printf("Average Income: $%.2f\n", total_income / income_count);
        printf("Max Income: $%.2f, Min Income: $%.2f\n", max_income, 
               (min_income == 999999.0) ? 0.0 : min_income);
    }
    
    if(expense_count > 0) {
        printf("Average Expense: $%.2f\n", total_expenses / expense_count);
        printf("Max Expense: $%.2f, Min Expense: $%.2f\n", max_expense, 
               (min_expense == 999999.0) ? 0.0 : min_expense);
    }
}
