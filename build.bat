@echo off
REM Personal Finance Management System - Build Script for Windows
REM This script compiles the PFMS project on Windows systems

echo === Personal Finance Management System - Build Script ===
echo.

REM Create necessary directories
echo Creating directories...
if not exist "obj" mkdir obj
if not exist "data" mkdir data

REM Compile all source files
echo Compiling source files...
gcc -Wall -Wextra -std=c99 -Iinclude -c src/main.c -o obj/main.o
if errorlevel 1 goto error

gcc -Wall -Wextra -std=c99 -Iinclude -c src/user.c -o obj/user.o
if errorlevel 1 goto error

gcc -Wall -Wextra -std=c99 -Iinclude -c src/transaction.c -o obj/transaction.o
if errorlevel 1 goto error

gcc -Wall -Wextra -std=c99 -Iinclude -c src/budget.c -o obj/budget.o
if errorlevel 1 goto error

gcc -Wall -Wextra -std=c99 -Iinclude -c src/utils.c -o obj/utils.o
if errorlevel 1 goto error

gcc -Wall -Wextra -std=c99 -Iinclude -c src/file_operations.c -o obj/file_operations.o
if errorlevel 1 goto error

gcc -Wall -Wextra -std=c99 -Iinclude -c src/reports.c -o obj/reports.o
if errorlevel 1 goto error

REM Link object files to create executable
echo Linking object files...
gcc obj/*.o -o finance_manager.exe
if errorlevel 1 goto error

echo.
echo *** BUILD SUCCESSFUL! ***
echo.
echo The Personal Finance Management System has been compiled successfully.
echo Run the program with: finance_manager.exe
echo.
echo Project Features:
echo - User account management
echo - Transaction recording and tracking
echo - Budget planning and monitoring
echo - Financial reports and analytics
echo - Data persistence with file I/O
echo - Sorting and searching capabilities
echo - Advanced pointer operations demo
echo.
echo C Programming Concepts Demonstrated:
echo - All fundamental C concepts
echo - Structured programming principles
echo - Memory management with pointers
echo - File handling and data persistence
echo - Algorithm implementation (sorting/searching)
echo.
goto end

:error
echo.
echo *** BUILD FAILED! ***
echo Please check the error messages above and fix any issues.
echo Make sure you have GCC compiler installed and accessible.
pause
exit /b 1

:end
echo Build completed successfully!
pause
