#include "../include/finance.h"

// External global variables
extern int total_users;

// Static variable for user ID generation
static int next_user_id = 1;

/**
 * Creates a new user account
 * Uses arrays, structures, and basic I/O
 */
int create_user(User users[], char* name, char* email) {
    // Check if maximum users reached
    if(total_users >= MAX_USERS) {
        return -1;
    }
    
    // Check if user already exists
    for(int i = 0; i < total_users; i++) {
        if(strcmp(users[i].name, name) == 0) {
            printf("User with this name already exists!\n");
            return -1;
        }
    }
    
    // Create new user
    users[total_users].user_id = next_user_id++;
    strcpy(users[total_users].name, name);
    strcpy(users[total_users].email, email);
    users[total_users].total_income = 0.0;
    users[total_users].total_expenses = 0.0;
    users[total_users].current_balance = 0.0;
    
    total_users++;
    return users[total_users - 1].user_id;
}

/**
 * User login function
 * Uses loops and string comparison
 */
int login_user(User users[], char* name) {
    // Linear search through users array
    for(int i = 0; i < total_users; i++) {
        if(strcmp(users[i].name, name) == 0) {
            return users[i].user_id;
        }
    }
    return -1; // User not found
}

/**
 * Display user profile information
 * Uses structures and formatted output
 */
void display_user_profile(User users[], int user_id) {
    // Find user by ID
    for(int i = 0; i < total_users; i++) {
        if(users[i].user_id == user_id) {
            printf("\n=== User Profile ===\n");
            printf("User ID: %d\n", users[i].user_id);
            printf("Name: %s\n", users[i].name);
            printf("Email: %s\n", users[i].email);
            printf("Total Income: $%.2f\n", users[i].total_income);
            printf("Total Expenses: $%.2f\n", users[i].total_expenses);
            printf("Current Balance: $%.2f\n", users[i].current_balance);
            
            // Financial health indicator using if-else
            if(users[i].current_balance > 1000) {
                printf("Financial Status: Excellent\n");
            } else if(users[i].current_balance > 500) {
                printf("Financial Status: Good\n");
            } else if(users[i].current_balance > 0) {
                printf("Financial Status: Fair\n");
            } else {
                printf("Financial Status: Needs Attention\n");
            }
            return;
        }
    }
    printf("User not found!\n");
}

/**
 * Update user balance after transaction
 * Uses pointer arithmetic and operators
 */
void update_user_balance(User users[], int user_id, float amount, char type) {
    User* user_ptr = users; // Pointer to users array
    
    // Use pointer arithmetic to find user
    for(int i = 0; i < total_users; i++, user_ptr++) {
        if(user_ptr->user_id == user_id) {
            if(type == 'I') { // Income
                user_ptr->total_income += amount;
                user_ptr->current_balance += amount;
            } else if(type == 'E') { // Expense
                user_ptr->total_expenses += amount;
                user_ptr->current_balance -= amount;
            }
            break;
        }
    }
}

/**
 * Get user by ID using pointer manipulation
 * Demonstrates pointer to pointer concept
 */
User* get_user_by_id(User users[], int user_id) {
    User* user_ptr = users;
    User** ptr_to_ptr = &user_ptr; // Pointer to pointer
    
    for(int i = 0; i < total_users; i++) {
        if((*ptr_to_ptr)->user_id == user_id) {
            return *ptr_to_ptr;
        }
        (*ptr_to_ptr)++; // Move pointer to next user
    }
    return NULL; // User not found
}

/**
 * Display all users (admin function)
 * Uses loops and arrays
 */
void display_all_users(User users[]) {
    if(total_users == 0) {
        printf("No users registered yet.\n");
        return;
    }
    
    printf("\n=== All Registered Users ===\n");
    printf("%-5s %-20s %-25s %-12s %-12s %-12s\n", 
           "ID", "Name", "Email", "Income", "Expenses", "Balance");
    printf("--------------------------------------------------------------------------------\n");
    
    // Use for loop to display all users
    for(int i = 0; i < total_users; i++) {
        printf("%-5d %-20s %-25s $%-11.2f $%-11.2f $%-11.2f\n",
               users[i].user_id,
               users[i].name,
               users[i].email,
               users[i].total_income,
               users[i].total_expenses,
               users[i].current_balance);
    }
}

/**
 * Calculate total system balance across all users
 * Uses register variables for optimization
 */
float calculate_total_system_balance(User users[]) {
    register float total_balance = 0.0; // Register variable for frequent access
    register int i; // Register variable for loop counter
    
    for(i = 0; i < total_users; i++) {
        total_balance += users[i].current_balance;
    }
    
    return total_balance;
}

/**
 * Find user with highest balance
 * Uses comparison operators and conditional logic
 */
int find_richest_user(User users[]) {
    if(total_users == 0) return -1;
    
    int richest_user_id = users[0].user_id;
    float highest_balance = users[0].current_balance;
    
    // Use while loop for demonstration
    int i = 1;
    while(i < total_users) {
        if(users[i].current_balance > highest_balance) {
            highest_balance = users[i].current_balance;
            richest_user_id = users[i].user_id;
        }
        i++;
    }
    
    return richest_user_id;
}

/**
 * Validate user email format
 * Uses string manipulation and logical operators
 */
int validate_email(char* email) {
    int at_count = 0;
    int dot_count = 0;
    int length = strlen(email);
    
    // Check basic email format using logical operators
    if(length < 5 || length > MAX_NAME_LENGTH - 1) {
        return 0; // Invalid length
    }
    
    // Count @ and . symbols
    for(int i = 0; i < length; i++) {
        if(email[i] == '@') at_count++;
        if(email[i] == '.') dot_count++;
    }
    
    // Basic validation: exactly one @ and at least one .
    return (at_count == 1 && dot_count >= 1);
}

/**
 * Reset user statistics
 * Uses do-while loop for confirmation
 */
void reset_user_stats(User users[], int user_id) {
    char confirmation;
    
    do {
        printf("Are you sure you want to reset all statistics? (y/n): ");
        scanf(" %c", &confirmation);
        
        if(confirmation == 'y' || confirmation == 'Y') {
            for(int i = 0; i < total_users; i++) {
                if(users[i].user_id == user_id) {
                    users[i].total_income = 0.0;
                    users[i].total_expenses = 0.0;
                    users[i].current_balance = 0.0;
                    printf("User statistics reset successfully!\n");
                    return;
                }
            }
            printf("User not found!\n");
            return;
        } else if(confirmation == 'n' || confirmation == 'N') {
            printf("Reset cancelled.\n");
            return;
        } else {
            printf("Invalid input. Please enter 'y' or 'n'.\n");
        }
    } while(1); // Continue until valid input
}
