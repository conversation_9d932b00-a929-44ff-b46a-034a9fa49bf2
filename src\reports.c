#include "../include/finance.h"

// External global variables
extern int total_transactions;

/**
 * Generate monthly financial report
 * Uses loops, calculations, and formatted output
 */
void generate_monthly_report(User users[], Transaction transactions[], int user_id, char* month) {
    printf("\n=== Monthly Financial Report for %s ===\n", month);
    
    float total_income = 0.0, total_expenses = 0.0;
    int income_count = 0, expense_count = 0;
    
    // Arrays to store category-wise data
    char income_categories[MAX_CATEGORIES][MAX_CATEGORY_LENGTH];
    char expense_categories[MAX_CATEGORIES][MAX_CATEGORY_LENGTH];
    float income_amounts[MAX_CATEGORIES] = {0};
    float expense_amounts[MAX_CATEGORIES] = {0};
    int income_cat_count = 0, expense_cat_count = 0;
    
    // Process transactions for the specified month
    for(int i = 0; i < total_transactions; i++) {
        if(transactions[i].user_id == user_id) {
            // Extract month-year from date (DD-MM-YYYY -> MM-YYYY)
            char trans_month[8];
            strncpy(trans_month, transactions[i].date + 3, 7);
            trans_month[7] = '\0';
            
            if(strcmp(trans_month, month) == 0) {
                if(transactions[i].type == 'I') {
                    total_income += transactions[i].amount;
                    income_count++;
                    
                    // Find or add income category
                    int found = 0;
                    for(int j = 0; j < income_cat_count; j++) {
                        if(strcmp(income_categories[j], transactions[i].category) == 0) {
                            income_amounts[j] += transactions[i].amount;
                            found = 1;
                            break;
                        }
                    }
                    if(!found && income_cat_count < MAX_CATEGORIES) {
                        strcpy(income_categories[income_cat_count], transactions[i].category);
                        income_amounts[income_cat_count] = transactions[i].amount;
                        income_cat_count++;
                    }
                    
                } else if(transactions[i].type == 'E') {
                    total_expenses += transactions[i].amount;
                    expense_count++;
                    
                    // Find or add expense category
                    int found = 0;
                    for(int j = 0; j < expense_cat_count; j++) {
                        if(strcmp(expense_categories[j], transactions[i].category) == 0) {
                            expense_amounts[j] += transactions[i].amount;
                            found = 1;
                            break;
                        }
                    }
                    if(!found && expense_cat_count < MAX_CATEGORIES) {
                        strcpy(expense_categories[expense_cat_count], transactions[i].category);
                        expense_amounts[expense_cat_count] = transactions[i].amount;
                        expense_cat_count++;
                    }
                }
            }
        }
    }
    
    // Display summary using conditional statements
    printf("\n--- Summary ---\n");
    printf("Total Income: $%.2f (%d transactions)\n", total_income, income_count);
    printf("Total Expenses: $%.2f (%d transactions)\n", total_expenses, expense_count);
    printf("Net Income: $%.2f\n", total_income - total_expenses);
    
    if(total_income > 0) {
        float savings_rate = ((total_income - total_expenses) / total_income) * 100.0;
        printf("Savings Rate: %.1f%%\n", savings_rate);
    }
    
    // Display income breakdown
    if(income_cat_count > 0) {
        printf("\n--- Income Breakdown ---\n");
        printf("%-20s %-10s %-10s\n", "Category", "Amount", "Percentage");
        printf("----------------------------------------\n");
        
        for(int i = 0; i < income_cat_count; i++) {
            float percentage = (income_amounts[i] / total_income) * 100.0;
            printf("%-20s $%-9.2f %.1f%%\n", 
                   income_categories[i], income_amounts[i], percentage);
        }
    }
    
    // Display expense breakdown
    if(expense_cat_count > 0) {
        printf("\n--- Expense Breakdown ---\n");
        printf("%-20s %-10s %-10s\n", "Category", "Amount", "Percentage");
        printf("----------------------------------------\n");
        
        for(int i = 0; i < expense_cat_count; i++) {
            float percentage = (expense_amounts[i] / total_expenses) * 100.0;
            printf("%-20s $%-9.2f %.1f%%\n", 
                   expense_categories[i], expense_amounts[i], percentage);
        }
    }
    
    // Financial health analysis using if-else
    printf("\n--- Financial Health Analysis ---\n");
    float net_income = total_income - total_expenses;
    
    if(net_income > 0) {
        if(net_income > total_income * 0.2) {
            printf("Excellent! You're saving more than 20%% of your income.\n");
        } else if(net_income > total_income * 0.1) {
            printf("Good! You're saving between 10-20%% of your income.\n");
        } else {
            printf("Fair. Try to increase your savings rate above 10%%.\n");
        }
    } else {
        printf("Warning! You're spending more than you earn this month.\n");
        printf("Consider reviewing your expenses and creating a budget.\n");
    }
}

/**
 * Generate category-wise report
 * Uses arrays, loops, and statistical calculations
 */
void generate_category_report(Transaction transactions[], int user_id) {
    printf("\n=== Category-wise Financial Report ===\n");
    
    // Arrays to store category data
    char categories[MAX_CATEGORIES][MAX_CATEGORY_LENGTH];
    float income_totals[MAX_CATEGORIES] = {0};
    float expense_totals[MAX_CATEGORIES] = {0};
    int transaction_counts[MAX_CATEGORIES] = {0};
    int category_count = 0;
    
    // Collect category data using for loop
    for(int i = 0; i < total_transactions; i++) {
        if(transactions[i].user_id == user_id) {
            
            // Find or add category
            int found = 0;
            for(int j = 0; j < category_count; j++) {
                if(strcmp(categories[j], transactions[i].category) == 0) {
                    if(transactions[i].type == 'I') {
                        income_totals[j] += transactions[i].amount;
                    } else {
                        expense_totals[j] += transactions[i].amount;
                    }
                    transaction_counts[j]++;
                    found = 1;
                    break;
                }
            }
            
            // Add new category
            if(!found && category_count < MAX_CATEGORIES) {
                strcpy(categories[category_count], transactions[i].category);
                if(transactions[i].type == 'I') {
                    income_totals[category_count] = transactions[i].amount;
                } else {
                    expense_totals[category_count] = transactions[i].amount;
                }
                transaction_counts[category_count] = 1;
                category_count++;
            }
        }
    }
    
    if(category_count == 0) {
        printf("No transactions found for category report.\n");
        return;
    }
    
    // Display category report
    printf("%-20s %-10s %-10s %-10s %-10s\n", 
           "Category", "Income", "Expenses", "Net", "Transactions");
    printf("----------------------------------------------------------------\n");
    
    float total_income = 0.0, total_expenses = 0.0;
    
    // Use while loop for display
    int i = 0;
    while(i < category_count) {
        float net_amount = income_totals[i] - expense_totals[i];
        
        printf("%-20s $%-9.2f $%-9.2f $%-9.2f %-10d\n",
               categories[i],
               income_totals[i],
               expense_totals[i],
               net_amount,
               transaction_counts[i]);
        
        total_income += income_totals[i];
        total_expenses += expense_totals[i];
        i++;
    }
    
    printf("----------------------------------------------------------------\n");
    printf("%-20s $%-9.2f $%-9.2f $%-9.2f\n", 
           "TOTAL", total_income, total_expenses, total_income - total_expenses);
    
    // Find highest spending category using comparison
    if(category_count > 0) {
        int highest_expense_index = 0;
        for(int j = 1; j < category_count; j++) {
            if(expense_totals[j] > expense_totals[highest_expense_index]) {
                highest_expense_index = j;
            }
        }
        
        printf("\nHighest spending category: %s ($%.2f)\n", 
               categories[highest_expense_index], 
               expense_totals[highest_expense_index]);
    }
}

/**
 * Display financial summary
 * Uses user data and calculations
 */
void display_financial_summary(User users[], Transaction transactions[], int user_id) {
    printf("\n=== Financial Summary ===\n");
    
    // Find user information
    User* current_user = NULL;
    for(int i = 0; i < total_users; i++) {
        if(users[i].user_id == user_id) {
            current_user = &users[i];
            break;
        }
    }
    
    if(current_user == NULL) {
        printf("User not found!\n");
        return;
    }
    
    // Display user information
    printf("User: %s\n", current_user->name);
    printf("Email: %s\n", current_user->email);
    printf("Current Balance: $%.2f\n", current_user->current_balance);
    printf("Total Income: $%.2f\n", current_user->total_income);
    printf("Total Expenses: $%.2f\n", current_user->total_expenses);
    
    // Calculate additional statistics
    int total_user_transactions = 0;
    float avg_transaction_amount = 0.0;
    float largest_income = 0.0, largest_expense = 0.0;
    
    // Use do-while loop for transaction analysis
    if(total_transactions > 0) {
        int i = 0;
        do {
            if(transactions[i].user_id == user_id) {
                total_user_transactions++;
                avg_transaction_amount += transactions[i].amount;
                
                if(transactions[i].type == 'I' && transactions[i].amount > largest_income) {
                    largest_income = transactions[i].amount;
                }
                if(transactions[i].type == 'E' && transactions[i].amount > largest_expense) {
                    largest_expense = transactions[i].amount;
                }
            }
            i++;
        } while(i < total_transactions);
        
        if(total_user_transactions > 0) {
            avg_transaction_amount /= total_user_transactions;
        }
    }
    
    printf("\n--- Transaction Statistics ---\n");
    printf("Total Transactions: %d\n", total_user_transactions);
    printf("Average Transaction Amount: $%.2f\n", avg_transaction_amount);
    printf("Largest Income Transaction: $%.2f\n", largest_income);
    printf("Largest Expense Transaction: $%.2f\n", largest_expense);
    
    // Financial ratios and analysis
    printf("\n--- Financial Analysis ---\n");
    
    if(current_user->total_income > 0) {
        float expense_ratio = (current_user->total_expenses / current_user->total_income) * 100.0;
        float savings_ratio = ((current_user->total_income - current_user->total_expenses) / current_user->total_income) * 100.0;
        
        printf("Expense Ratio: %.1f%% of income\n", expense_ratio);
        printf("Savings Ratio: %.1f%% of income\n", savings_ratio);
        
        // Financial health assessment using nested if-else
        if(savings_ratio >= 20.0) {
            printf("Financial Health: Excellent (Saving 20%% or more)\n");
        } else if(savings_ratio >= 10.0) {
            printf("Financial Health: Good (Saving 10-20%%)\n");
        } else if(savings_ratio >= 0.0) {
            printf("Financial Health: Fair (Saving less than 10%%)\n");
        } else {
            printf("Financial Health: Poor (Spending more than earning)\n");
        }
    }
    
    // Recommendations based on balance
    printf("\n--- Recommendations ---\n");
    if(current_user->current_balance < 0) {
        printf("• You have a negative balance. Focus on reducing expenses.\n");
        printf("• Consider creating a strict budget to control spending.\n");
    } else if(current_user->current_balance < 500) {
        printf("• Build an emergency fund of at least $1000.\n");
        printf("• Look for ways to increase your income.\n");
    } else if(current_user->current_balance < 1000) {
        printf("• You're on the right track. Keep building your savings.\n");
        printf("• Consider setting up automatic savings.\n");
    } else {
        printf("• Great job maintaining a healthy balance!\n");
        printf("• Consider investing surplus funds for long-term growth.\n");
    }
}

/**
 * Generate yearly financial report
 * Uses advanced calculations and data analysis
 */
void generate_yearly_report(User users[], Transaction transactions[], int user_id, int year) {
    printf("\n=== Yearly Financial Report for %d ===\n", year);
    
    float monthly_income[12] = {0};
    float monthly_expenses[12] = {0};
    char month_names[12][10] = {"January", "February", "March", "April", "May", "June",
                               "July", "August", "September", "October", "November", "December"};
    
    // Process transactions for the year
    for(int i = 0; i < total_transactions; i++) {
        if(transactions[i].user_id == user_id) {
            // Extract year from date
            int trans_year;
            sscanf(transactions[i].date + 6, "%d", &trans_year);
            
            if(trans_year == year) {
                // Extract month
                int month;
                sscanf(transactions[i].date + 3, "%d", &month);
                
                if(month >= 1 && month <= 12) {
                    if(transactions[i].type == 'I') {
                        monthly_income[month - 1] += transactions[i].amount;
                    } else {
                        monthly_expenses[month - 1] += transactions[i].amount;
                    }
                }
            }
        }
    }
    
    // Display monthly breakdown
    printf("\n%-12s %-10s %-10s %-10s\n", "Month", "Income", "Expenses", "Net");
    printf("--------------------------------------------\n");
    
    float total_year_income = 0.0, total_year_expenses = 0.0;
    
    for(int i = 0; i < 12; i++) {
        float net = monthly_income[i] - monthly_expenses[i];
        printf("%-12s $%-9.2f $%-9.2f $%-9.2f\n", 
               month_names[i], monthly_income[i], monthly_expenses[i], net);
        
        total_year_income += monthly_income[i];
        total_year_expenses += monthly_expenses[i];
    }
    
    printf("--------------------------------------------\n");
    printf("%-12s $%-9.2f $%-9.2f $%-9.2f\n", 
           "TOTAL", total_year_income, total_year_expenses, 
           total_year_income - total_year_expenses);
    
    // Calculate averages
    printf("\n--- Yearly Statistics ---\n");
    printf("Average Monthly Income: $%.2f\n", total_year_income / 12.0);
    printf("Average Monthly Expenses: $%.2f\n", total_year_expenses / 12.0);
    printf("Average Monthly Savings: $%.2f\n", (total_year_income - total_year_expenses) / 12.0);
    
    // Find best and worst months
    int best_month = 0, worst_month = 0;
    float best_net = monthly_income[0] - monthly_expenses[0];
    float worst_net = best_net;
    
    for(int i = 1; i < 12; i++) {
        float net = monthly_income[i] - monthly_expenses[i];
        if(net > best_net) {
            best_net = net;
            best_month = i;
        }
        if(net < worst_net) {
            worst_net = net;
            worst_month = i;
        }
    }
    
    printf("\nBest Month: %s (Net: $%.2f)\n", month_names[best_month], best_net);
    printf("Worst Month: %s (Net: $%.2f)\n", month_names[worst_month], worst_net);
}
