#include "../include/finance.h"

// External global variables
extern int total_users;
extern int total_transactions;

/**
 * Save users data to file
 * Uses file I/O operations and structures
 */
void save_users_to_file(User users[]) {
    FILE* file = fopen("data/users.dat", "wb");
    
    if(file == NULL) {
        printf("Error: Could not create data directory or file.\n");
        // Try to create data directory and file in current directory
        file = fopen("users.dat", "wb");
        if(file == NULL) {
            printf("Error: Could not save user data.\n");
            return;
        }
    }
    
    // Write total number of users first
    fwrite(&total_users, sizeof(int), 1, file);
    
    // Write all user records
    if(total_users > 0) {
        fwrite(users, sizeof(User), total_users, file);
    }
    
    fclose(file);
    printf("User data saved successfully.\n");
}

/**
 * Load users data from file
 * Uses file I/O operations and error handling
 */
void load_users_from_file(User users[]) {
    FILE* file = fopen("data/users.dat", "rb");
    
    // Try alternative location if data directory doesn't exist
    if(file == NULL) {
        file = fopen("users.dat", "rb");
        if(file == NULL) {
            printf("No existing user data found. Starting fresh.\n");
            total_users = 0;
            return;
        }
    }
    
    // Read total number of users
    if(fread(&total_users, sizeof(int), 1, file) != 1) {
        printf("Error reading user count from file.\n");
        total_users = 0;
        fclose(file);
        return;
    }
    
    // Validate user count
    if(total_users < 0 || total_users > MAX_USERS) {
        printf("Invalid user count in file. Starting fresh.\n");
        total_users = 0;
        fclose(file);
        return;
    }
    
    // Read user records if any exist
    if(total_users > 0) {
        size_t read_count = fread(users, sizeof(User), total_users, file);
        if(read_count != total_users) {
            printf("Warning: Could not read all user records.\n");
            total_users = read_count;
        }
    }
    
    fclose(file);
    printf("Loaded %d user(s) from file.\n", total_users);
}

/**
 * Save transactions data to file
 * Uses file I/O with binary mode
 */
void save_transactions_to_file(Transaction transactions[]) {
    FILE* file = fopen("data/transactions.dat", "wb");
    
    if(file == NULL) {
        file = fopen("transactions.dat", "wb");
        if(file == NULL) {
            printf("Error: Could not save transaction data.\n");
            return;
        }
    }
    
    // Write total number of transactions
    fwrite(&total_transactions, sizeof(int), 1, file);
    
    // Write all transaction records using loop
    if(total_transactions > 0) {
        for(int i = 0; i < total_transactions; i++) {
            fwrite(&transactions[i], sizeof(Transaction), 1, file);
        }
    }
    
    fclose(file);
    printf("Transaction data saved successfully.\n");
}

/**
 * Load transactions data from file
 * Uses file I/O with error checking
 */
void load_transactions_from_file(Transaction transactions[]) {
    FILE* file = fopen("data/transactions.dat", "rb");
    
    if(file == NULL) {
        file = fopen("transactions.dat", "rb");
        if(file == NULL) {
            printf("No existing transaction data found. Starting fresh.\n");
            total_transactions = 0;
            return;
        }
    }
    
    // Read total number of transactions
    if(fread(&total_transactions, sizeof(int), 1, file) != 1) {
        printf("Error reading transaction count from file.\n");
        total_transactions = 0;
        fclose(file);
        return;
    }
    
    // Validate transaction count
    if(total_transactions < 0 || total_transactions > MAX_TRANSACTIONS) {
        printf("Invalid transaction count in file. Starting fresh.\n");
        total_transactions = 0;
        fclose(file);
        return;
    }
    
    // Read transaction records using while loop
    int i = 0;
    while(i < total_transactions) {
        if(fread(&transactions[i], sizeof(Transaction), 1, file) != 1) {
            printf("Warning: Could not read all transaction records.\n");
            total_transactions = i;
            break;
        }
        i++;
    }
    
    fclose(file);
    printf("Loaded %d transaction(s) from file.\n", total_transactions);
}

/**
 * Save budgets data to file
 * Uses file I/O operations
 */
void save_budgets_to_file(Budget budgets[]) {
    FILE* file = fopen("data/budgets.dat", "wb");
    
    if(file == NULL) {
        file = fopen("budgets.dat", "wb");
        if(file == NULL) {
            printf("Error: Could not save budget data.\n");
            return;
        }
    }
    
    // Count total budgets first
    int budget_count = 0;
    for(int i = 0; i < MAX_USERS * MAX_CATEGORIES; i++) {
        if(budgets[i].user_id != 0) { // Assuming 0 means empty slot
            budget_count++;
        }
    }
    
    // Write budget count
    fwrite(&budget_count, sizeof(int), 1, file);
    
    // Write budget records using do-while loop
    if(budget_count > 0) {
        int i = 0;
        int written = 0;
        do {
            if(budgets[i].user_id != 0) {
                fwrite(&budgets[i], sizeof(Budget), 1, file);
                written++;
            }
            i++;
        } while(written < budget_count && i < MAX_USERS * MAX_CATEGORIES);
    }
    
    fclose(file);
    printf("Budget data saved successfully.\n");
}

/**
 * Load budgets data from file
 * Uses file I/O with validation
 */
void load_budgets_from_file(Budget budgets[]) {
    FILE* file = fopen("data/budgets.dat", "rb");
    
    if(file == NULL) {
        file = fopen("budgets.dat", "rb");
        if(file == NULL) {
            printf("No existing budget data found. Starting fresh.\n");
            // Initialize budget array
            for(int i = 0; i < MAX_USERS * MAX_CATEGORIES; i++) {
                budgets[i].user_id = 0; // Mark as empty
            }
            return;
        }
    }
    
    // Initialize budget array first
    for(int i = 0; i < MAX_USERS * MAX_CATEGORIES; i++) {
        budgets[i].user_id = 0;
    }
    
    int budget_count;
    if(fread(&budget_count, sizeof(int), 1, file) != 1) {
        printf("Error reading budget count from file.\n");
        fclose(file);
        return;
    }
    
    // Validate budget count
    if(budget_count < 0 || budget_count > MAX_USERS * MAX_CATEGORIES) {
        printf("Invalid budget count in file.\n");
        fclose(file);
        return;
    }
    
    // Read budget records
    for(int i = 0; i < budget_count; i++) {
        if(fread(&budgets[i], sizeof(Budget), 1, file) != 1) {
            printf("Warning: Could not read all budget records.\n");
            break;
        }
    }
    
    fclose(file);
    printf("Loaded %d budget(s) from file.\n", budget_count);
}

/**
 * Export data to CSV format
 * Uses text file I/O and string formatting
 */
void export_transactions_to_csv(Transaction transactions[], int user_id, char* filename) {
    FILE* file = fopen(filename, "w");
    
    if(file == NULL) {
        printf("Error: Could not create CSV file.\n");
        return;
    }
    
    // Write CSV header
    fprintf(file, "Transaction ID,Date,Category,Description,Amount,Type\n");
    
    // Write transaction data using for loop
    int exported_count = 0;
    for(int i = 0; i < total_transactions; i++) {
        if(transactions[i].user_id == user_id) {
            fprintf(file, "%d,%s,%s,\"%s\",%.2f,%c\n",
                    transactions[i].transaction_id,
                    transactions[i].date,
                    transactions[i].category,
                    transactions[i].description,
                    transactions[i].amount,
                    transactions[i].type);
            exported_count++;
        }
    }
    
    fclose(file);
    printf("Exported %d transactions to %s\n", exported_count, filename);
}

/**
 * Import transactions from CSV file
 * Uses text file I/O and string parsing
 */
int import_transactions_from_csv(Transaction transactions[], int user_id, char* filename) {
    FILE* file = fopen(filename, "r");
    
    if(file == NULL) {
        printf("Error: Could not open CSV file for import.\n");
        return 0;
    }
    
    char line[256];
    int imported_count = 0;
    
    // Skip header line
    if(fgets(line, sizeof(line), file) == NULL) {
        printf("Error: Empty CSV file.\n");
        fclose(file);
        return 0;
    }
    
    // Read and parse each line
    while(fgets(line, sizeof(line), file) != NULL && total_transactions < MAX_TRANSACTIONS) {
        char date[11], category[MAX_CATEGORY_LENGTH], description[MAX_DESCRIPTION_LENGTH];
        float amount;
        char type;
        
        // Parse CSV line (simplified parsing)
        if(sscanf(line, "%*d,%10[^,],%29[^,],\"%99[^\"]\",%f,%c",
                  date, category, description, &amount, &type) == 5) {
            
            // Add transaction
            if(add_transaction(transactions, user_id, date, category, 
                             description, amount, type) != -1) {
                imported_count++;
            }
        }
    }
    
    fclose(file);
    printf("Imported %d transactions from %s\n", imported_count, filename);
    return imported_count;
}

/**
 * Create backup of all data
 * Uses file operations and string manipulation
 */
void create_backup(User users[], Transaction transactions[], Budget budgets[]) {
    char backup_filename[100];
    time_t t = time(NULL);
    struct tm* tm_info = localtime(&t);
    
    // Create backup filename with timestamp
    sprintf(backup_filename, "backup_%04d%02d%02d_%02d%02d%02d.dat",
            tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
            tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);
    
    FILE* file = fopen(backup_filename, "wb");
    if(file == NULL) {
        printf("Error: Could not create backup file.\n");
        return;
    }
    
    // Write backup header
    char header[] = "PFMS_BACKUP_V1.0";
    fwrite(header, sizeof(char), strlen(header), file);
    
    // Write users data
    fwrite(&total_users, sizeof(int), 1, file);
    if(total_users > 0) {
        fwrite(users, sizeof(User), total_users, file);
    }
    
    // Write transactions data
    fwrite(&total_transactions, sizeof(int), 1, file);
    if(total_transactions > 0) {
        fwrite(transactions, sizeof(Transaction), total_transactions, file);
    }
    
    // Write budgets data (count non-empty budgets first)
    int budget_count = 0;
    for(int i = 0; i < MAX_USERS * MAX_CATEGORIES; i++) {
        if(budgets[i].user_id != 0) budget_count++;
    }
    
    fwrite(&budget_count, sizeof(int), 1, file);
    for(int i = 0; i < MAX_USERS * MAX_CATEGORIES; i++) {
        if(budgets[i].user_id != 0) {
            fwrite(&budgets[i], sizeof(Budget), 1, file);
        }
    }
    
    fclose(file);
    printf("Backup created successfully: %s\n", backup_filename);
}

/**
 * Check if data files exist
 * Uses file operations and conditional logic
 */
int check_data_files_exist(void) {
    FILE* users_file = fopen("data/users.dat", "rb");
    FILE* trans_file = fopen("data/transactions.dat", "rb");
    FILE* budget_file = fopen("data/budgets.dat", "rb");
    
    int files_exist = 0;
    
    if(users_file != NULL) {
        files_exist++;
        fclose(users_file);
    }
    
    if(trans_file != NULL) {
        files_exist++;
        fclose(trans_file);
    }
    
    if(budget_file != NULL) {
        files_exist++;
        fclose(budget_file);
    }
    
    return files_exist; // Return number of existing files
}
