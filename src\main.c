#include "../include/finance.h"

// Global variables definition
int total_users = 0;
int total_transactions = 0;
int current_user_id = -1;

// Static variable for menu choice tracking
static int menu_access_count = 0;

int main() {
    // Local variables
    User users[MAX_USERS];
    Transaction transactions[MAX_TRANSACTIONS];
    Budget budgets[MAX_USERS * MAX_CATEGORIES];

    int choice;
    char name[MAX_NAME_LENGTH], email[MAX_NAME_LENGTH];
    char date[11], category[MAX_CATEGORY_LENGTH], description[MAX_DESCRIPTION_LENGTH];
    float amount, budget_limit;
    char type, month[8];

    // Register variable for frequently used loop counter
    register int i;

    // Load existing data from files
    load_users_from_file(users);
    load_transactions_from_file(transactions);
    load_budgets_from_file(budgets);

    printf("=== Personal Finance Management System ===\n");
    printf("Welcome to your financial companion!\n\n");

    // Main program loop
    do {
        menu_access_count++; // Static variable increment
        display_menu();
        printf("Enter your choice: ");
        scanf("%d", &choice);
        clear_input_buffer();

        switch(choice) {
            case 1: // Create new user
                printf("\n--- Create New User ---\n");
                printf("Enter your name: ");
                fgets(name, sizeof(name), stdin);
                name[strcspn(name, "\n")] = 0; // Remove newline

                printf("Enter your email: ");
                fgets(email, sizeof(email), stdin);
                email[strcspn(email, "\n")] = 0;

                if(create_user(users, name, email) != -1) {
                    printf("User created successfully!\n");
                    save_users_to_file(users);
                } else {
                    printf("Failed to create user. Maximum users reached.\n");
                }
                break;

            case 2: // Login
                printf("\n--- User Login ---\n");
                printf("Enter your name: ");
                fgets(name, sizeof(name), stdin);
                name[strcspn(name, "\n")] = 0;

                current_user_id = login_user(users, name);
                if(current_user_id != -1) {
                    printf("Login successful! Welcome, %s!\n", name);
                } else {
                    printf("User not found. Please create an account first.\n");
                }
                break;

            case 3: // Add transaction
                if(current_user_id == -1) {
                    printf("Please login first!\n");
                    break;
                }

                printf("\n--- Add Transaction ---\n");
                printf("Enter date (DD-MM-YYYY): ");
                fgets(date, sizeof(date), stdin);
                date[strcspn(date, "\n")] = 0;

                if(!validate_date(date)) {
                    printf("Invalid date format!\n");
                    break;
                }

                printf("Enter category: ");
                fgets(category, sizeof(category), stdin);
                category[strcspn(category, "\n")] = 0;

                printf("Enter description: ");
                fgets(description, sizeof(description), stdin);
                description[strcspn(description, "\n")] = 0;

                printf("Enter amount: ");
                scanf("%f", &amount);
                clear_input_buffer();

                printf("Enter type (I for Income, E for Expense): ");
                scanf("%c", &type);
                clear_input_buffer();

                if(type != 'I' && type != 'E') {
                    printf("Invalid transaction type!\n");
                    break;
                }

                if(add_transaction(transactions, current_user_id, date, category,
                                 description, amount, type) != -1) {
                    update_user_balance(users, current_user_id, amount, type);
                    printf("Transaction added successfully!\n");
                    save_transactions_to_file(transactions);
                    save_users_to_file(users);
                } else {
                    printf("Failed to add transaction.\n");
                }
                break;

            case 4: // View transactions
                if(current_user_id == -1) {
                    printf("Please login first!\n");
                    break;
                }
                display_transactions(transactions, current_user_id);
                break;

            case 5: // Set budget
                if(current_user_id == -1) {
                    printf("Please login first!\n");
                    break;
                }

                printf("\n--- Set Budget ---\n");
                printf("Enter category: ");
                fgets(category, sizeof(category), stdin);
                category[strcspn(category, "\n")] = 0;

                printf("Enter budget limit: ");
                scanf("%f", &budget_limit);
                clear_input_buffer();

                printf("Enter month (MM-YYYY): ");
                fgets(month, sizeof(month), stdin);
                month[strcspn(month, "\n")] = 0;

                if(set_budget(budgets, current_user_id, category, budget_limit, month) != -1) {
                    printf("Budget set successfully!\n");
                    save_budgets_to_file(budgets);
                } else {
                    printf("Failed to set budget.\n");
                }
                break;

            case 6: // View budget status
                if(current_user_id == -1) {
                    printf("Please login first!\n");
                    break;
                }
                check_budget_status(budgets, transactions, current_user_id);
                break;

            case 7: // Generate reports
                if(current_user_id == -1) {
                    printf("Please login first!\n");
                    break;
                }

                printf("\n--- Reports Menu ---\n");
                printf("1. Monthly Report\n");
                printf("2. Category Report\n");
                printf("3. Financial Summary\n");
                printf("Enter choice: ");

                int report_choice;
                scanf("%d", &report_choice);
                clear_input_buffer();

                switch(report_choice) {
                    case 1:
                        printf("Enter month (MM-YYYY): ");
                        fgets(month, sizeof(month), stdin);
                        month[strcspn(month, "\n")] = 0;
                        generate_monthly_report(users, transactions, current_user_id, month);
                        break;
                    case 2:
                        generate_category_report(transactions, current_user_id);
                        break;
                    case 3:
                        display_financial_summary(users, transactions, current_user_id);
                        break;
                    default:
                        printf("Invalid choice!\n");
                }
                break;

            case 8: // Sort transactions
                if(current_user_id == -1) {
                    printf("Please login first!\n");
                    break;
                }

                printf("\n--- Sort Transactions ---\n");
                printf("1. Sort by Amount (Bubble Sort)\n");
                printf("2. Sort by Date (Selection Sort)\n");
                printf("Enter choice: ");

                int sort_choice;
                scanf("%d", &sort_choice);
                clear_input_buffer();

                // Create array of user's transactions for sorting
                Transaction user_transactions[MAX_TRANSACTIONS];
                int user_trans_count = 0;

                for(i = 0; i < total_transactions; i++) {
                    if(transactions[i].user_id == current_user_id) {
                        user_transactions[user_trans_count++] = transactions[i];
                    }
                }

                if(sort_choice == 1) {
                    bubble_sort_transactions_by_amount(user_transactions, user_trans_count);
                    printf("Transactions sorted by amount:\n");
                } else if(sort_choice == 2) {
                    selection_sort_transactions_by_date(user_transactions, user_trans_count);
                    printf("Transactions sorted by date:\n");
                } else {
                    printf("Invalid choice!\n");
                    break;
                }

                // Display sorted transactions
                printf("\n%-5s %-12s %-15s %-20s %-10s %-5s\n",
                       "ID", "Date", "Category", "Description", "Amount", "Type");
                printf("----------------------------------------------------------------\n");

                for(i = 0; i < user_trans_count; i++) {
                    printf("%-5d %-12s %-15s %-20s %-10.2f %-5c\n",
                           user_transactions[i].transaction_id,
                           user_transactions[i].date,
                           user_transactions[i].category,
                           user_transactions[i].description,
                           user_transactions[i].amount,
                           user_transactions[i].type);
                }
                break;

            case 9: // Search transactions
                if(current_user_id == -1) {
                    printf("Please login first!\n");
                    break;
                }

                printf("\n--- Search Transactions ---\n");
                printf("1. Search by Transaction ID\n");
                printf("2. Search by Category\n");
                printf("Enter choice: ");

                int search_choice;
                scanf("%d", &search_choice);
                clear_input_buffer();

                if(search_choice == 1) {
                    int trans_id;
                    printf("Enter transaction ID: ");
                    scanf("%d", &trans_id);
                    clear_input_buffer();

                    int found_index = search_transaction_by_id(transactions, trans_id);
                    if(found_index != -1 && transactions[found_index].user_id == current_user_id) {
                        printf("\nTransaction found:\n");
                        printf("ID: %d\n", transactions[found_index].transaction_id);
                        printf("Date: %s\n", transactions[found_index].date);
                        printf("Category: %s\n", transactions[found_index].category);
                        printf("Description: %s\n", transactions[found_index].description);
                        printf("Amount: %.2f\n", transactions[found_index].amount);
                        printf("Type: %c\n", transactions[found_index].type);
                    } else {
                        printf("Transaction not found or doesn't belong to you.\n");
                    }
                } else if(search_choice == 2) {
                    printf("Enter category: ");
                    fgets(category, sizeof(category), stdin);
                    category[strcspn(category, "\n")] = 0;
                    search_transactions_by_category(transactions, current_user_id, category);
                } else {
                    printf("Invalid choice!\n");
                }
                break;

            case 10: // View user profile
                if(current_user_id == -1) {
                    printf("Please login first!\n");
                    break;
                }
                display_user_profile(users, current_user_id);
                break;

            case 11: // Pointer operations demo
                if(current_user_id == -1) {
                    printf("Please login first!\n");
                    break;
                }

                printf("\n--- Advanced Pointer Operations ---\n");

                // Create array of user's transactions for pointer manipulation
                Transaction pointer_transactions[MAX_TRANSACTIONS];
                int pointer_trans_count = 0;

                for(i = 0; i < total_transactions; i++) {
                    if(transactions[i].user_id == current_user_id) {
                        pointer_transactions[pointer_trans_count++] = transactions[i];
                    }
                }

                if(pointer_trans_count > 0) {
                    printf("Processing transactions using pointer arithmetic...\n");
                    process_transactions_with_pointers(pointer_transactions, pointer_trans_count);

                    printf("\nSorting using pointer to pointer...\n");
                    Transaction* trans_ptr = pointer_transactions;
                    sort_using_pointer_to_pointer(&trans_ptr, pointer_trans_count);
                } else {
                    printf("No transactions found for pointer operations.\n");
                }
                break;

            case 0: // Exit
                printf("\nThank you for using Personal Finance Management System!\n");
                printf("Total menu accesses: %d\n", menu_access_count);

                // Save all data before exit
                save_users_to_file(users);
                save_transactions_to_file(transactions);
                save_budgets_to_file(budgets);
                break;

            default:
                printf("Invalid choice! Please try again.\n");
        }

        if(choice != 0) {
            printf("\nPress Enter to continue...");
            getchar();
        }

    } while(choice != 0);

    return 0;
}
